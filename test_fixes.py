#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الإصلاحات المطبقة على نظام إدارة الطلاب
"""

import sqlite3
import os
import sys

def test_database_structure():
    """اختبار بنية قاعدة البيانات"""
    print("🔍 اختبار بنية قاعدة البيانات...")
    
    try:
        conn = sqlite3.connect('students.db')
        cursor = conn.cursor()
        
        # التحقق من وجود الجدول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='students'")
        if not cursor.fetchone():
            print("❌ جدول الطلاب غير موجود")
            return False
        
        # التحقق من الأعمدة المطلوبة
        cursor.execute("PRAGMA table_info(students)")
        columns = [col[1] for col in cursor.fetchall()]
        
        required_columns = ['id', 'name', 'student_id', 'gender', 'nationality', 
                          'college', 'department', 'status', 'image_path', 
                          'enrollment_date', 'birth_date', 'level']
        
        missing_columns = [col for col in required_columns if col not in columns]
        if missing_columns:
            print(f"❌ أعمدة مفقودة: {missing_columns}")
            return False
        
        print("✅ بنية قاعدة البيانات صحيحة")
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار قاعدة البيانات: {e}")
        return False

def test_image_directory():
    """اختبار مجلد الصور"""
    print("🔍 اختبار مجلد الصور...")
    
    images_dir = "student_images"
    if not os.path.exists(images_dir):
        try:
            os.makedirs(images_dir)
            print("✅ تم إنشاء مجلد الصور")
        except Exception as e:
            print(f"❌ فشل في إنشاء مجلد الصور: {e}")
            return False
    else:
        print("✅ مجلد الصور موجود")
    
    return True

def test_imports():
    """اختبار استيراد المكتبات"""
    print("🔍 اختبار استيراد المكتبات...")
    
    try:
        import tkinter as tk
        from tkinter import ttk, messagebox
        import sqlite3
        from datetime import datetime
        import os
        from PIL import Image, ImageTk
        print("✅ جميع المكتبات متوفرة")
        return True
    except ImportError as e:
        print(f"❌ مكتبة مفقودة: {e}")
        return False

def test_student_management_import():
    """اختبار استيراد ملف نظام إدارة الطلاب"""
    print("🔍 اختبار استيراد نظام إدارة الطلاب...")
    
    try:
        import student_management
        print("✅ تم استيراد نظام إدارة الطلاب بنجاح")
        return True
    except Exception as e:
        print(f"❌ فشل في استيراد نظام إدارة الطلاب: {e}")
        return False

def test_validation_functions():
    """اختبار دوال التحقق"""
    print("🔍 اختبار دوال التحقق...")
    
    try:
        import student_management
        
        # إنشاء مثيل وهمي للاختبار
        class MockApp:
            def is_valid_date_any(self, date_str):
                return student_management.StudentManagementSystem.is_valid_date_any(self, date_str)
            
            def fix_date(self, date_str):
                return student_management.StudentManagementSystem.fix_date(self, date_str)
            
            def validate_student_data(self, *args):
                return student_management.StudentManagementSystem.validate_student_data(self, *args)
        
        app = MockApp()
        
        # اختبار التواريخ
        test_dates = [
            ("2023", True),
            ("2023-12", True),
            ("2023-12-25", True),
            ("invalid", False),
            ("2023-13", False),
            ("2023-12-32", False)
        ]
        
        for date_str, expected in test_dates:
            result = app.is_valid_date_any(date_str)
            if result != expected:
                print(f"❌ فشل اختبار التاريخ: {date_str} (متوقع: {expected}, الفعلي: {result})")
                return False
        
        # اختبار إكمال التواريخ
        assert app.fix_date("2023") == "2023-01-01"
        assert app.fix_date("2023-12") == "2023-12-01"
        assert app.fix_date("2023-12-25") == "2023-12-25"
        
        print("✅ دوال التحقق تعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"❌ فشل في اختبار دوال التحقق: {e}")
        return False

def main():
    """تشغيل جميع الاختبارات"""
    print("🚀 بدء اختبار الإصلاحات...")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_database_structure,
        test_image_directory,
        test_student_management_import,
        test_validation_functions
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"📊 النتائج: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! المشروع جاهز للاستخدام.")
    else:
        print("⚠️  بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
