# 🎓 دليل نظام تسجيل الدخول - نظام إدارة الطلاب

## 🔐 معلومات تسجيل الدخول

### بيانات الدخول الافتراضية:
- **اسم المستخدم**: `admin`
- **كلمة المرور**: `admin123`

## 🚀 كيفية الاستخدام

### 1. تشغيل النظام
```bash
python student_management.py
```

### 2. تسجيل الدخول
1. ستظهر نافذة تسجيل الدخول مع الشعار
2. أدخل اسم المستخدم: `admin`
3. أدخل كلمة المرور: `admin123`
4. اضغط "دخول" أو اضغط Enter

### 3. الوصول للنظام الرئيسي
بعد تسجيل الدخول بنجاح، ستفتح نافذة النظام الرئيسي لإدارة الطلاب.

## 🎨 مميزات نافذة تسجيل الدخول

### التصميم:
- ✅ واجهة عصرية وأنيقة
- ✅ شعار مخصص للنظام
- ✅ ألوان متناسقة ومريحة للعين
- ✅ تخطيط مركزي ومنظم

### الوظائف:
- ✅ تشفير كلمات المرور (SHA-256)
- ✅ قاعدة بيانات منفصلة للمستخدمين
- ✅ التحقق من صحة البيانات
- ✅ رسائل خطأ واضحة
- ✅ دعم مفتاح Enter للدخول السريع
- ✅ مسح كلمة المرور عند الخطأ

## 🔧 الملفات المضافة

### 1. قاعدة بيانات المستخدمين
- **الملف**: `users.db`
- **الوصف**: يحتوي على بيانات المستخدمين المشفرة

### 2. الشعار
- **الملف**: `logo.png`
- **الوصف**: شعار النظام المخصص
- **الإنشاء**: يتم إنشاؤه تلقائياً عبر `create_logo.py`

### 3. سكريبت إنشاء الشعار
- **الملف**: `create_logo.py`
- **الوصف**: ينشئ شعار النظام تلقائياً

## 🛡️ الأمان

### تشفير كلمات المرور:
- يتم تشفير جميع كلمات المرور باستخدام SHA-256
- لا يتم حفظ كلمات المرور بشكل واضح في قاعدة البيانات

### قاعدة البيانات:
- قاعدة بيانات منفصلة للمستخدمين
- حماية من SQL Injection
- التحقق من صحة البيانات

## 🎯 المستخدم الافتراضي

يتم إنشاء مستخدم افتراضي تلقائياً:
- **الاسم الكامل**: المدير العام
- **اسم المستخدم**: admin
- **كلمة المرور**: admin123 (مشفرة)
- **الصلاحية**: admin

## 📱 التوافق

- ✅ Windows
- ✅ macOS  
- ✅ Linux
- ✅ دعم اللغة العربية
- ✅ واجهة متجاوبة

## 🔄 التحديثات المضافة

### في الكود الرئيسي:
1. إضافة كلاس `LoginSystem`
2. تشفير كلمات المرور
3. قاعدة بيانات المستخدمين
4. واجهة تسجيل دخول عصرية
5. تكامل مع النظام الرئيسي

### المكتبات المضافة:
- `hashlib` - لتشفير كلمات المرور
- `filedialog` - لاختيار الملفات
- `shutil` - لنسخ الملفات

---

## 🎉 الآن النظام جاهز!

يمكنك الآن تشغيل النظام والاستمتاع بنافذة تسجيل دخول احترافية مع شعار مخصص! 🚀
