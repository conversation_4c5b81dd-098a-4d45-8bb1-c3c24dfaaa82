🎓 نظام إدارة الطلاب المتطور
============================

📋 معلومات التطبيق:
- الاسم: نظام إدارة الطلاب المتطور
- الإصدار: 2.3 (محسّن للعرض)
- حجم الملف: ~30 ميجابايت
- نوع الملف: تطبيق مستقل (.exe)

🆕 التحسينات الجديدة:
- نافذة تسجيل الدخول تظهر في وسط الشاشة من البداية
- تحسين موضع النافذة وثباتها
- منع تغيير حجم نافذة تسجيل الدخول
- تحسين وضوح نصوص "اسم المستخدم" و "كلمة المرور"
- تحسين تصميم حقول الإدخال مع إطارات أوضح
- خط أكبر وأوضح للنصوص (14pt بدلاً من 12pt)
- زيادة ارتفاع النافذة لعرض جميع العناصر بوضوح
- تحسين المساحات بين العناصر لعرض أفضل

🚀 كيفية التشغيل:
1. انقر نقراً مزدوجاً على "StudentManagement.exe"
2. ستظهر نافذة تسجيل الدخول مع الشعار
3. استخدم بيانات الدخول التالية:
   - اسم المستخدم: admin
   - كلمة المرور: admin123
4. اضغط "دخول" أو مفتاح Enter

🔐 بيانات تسجيل الدخول الافتراضية:
- المستخدم: admin
- كلمة المرور: admin123
- الصلاحية: مدير عام

✨ مميزات التطبيق:
- واجهة تسجيل دخول عصرية مع شعار
- إدارة كاملة لبيانات الطلاب
- رفع وعرض صور الطلاب
- بحث وتصفية متقدمة
- تصدير واستيراد البيانات
- واجهة عربية كاملة
- تشفير كلمات المرور
- قواعد بيانات آمنة

📁 الملفات التي سيتم إنشاؤها:
- students.db (قاعدة بيانات الطلاب)
- users.db (قاعدة بيانات المستخدمين)
- student_images/ (مجلد صور الطلاب)

🔧 متطلبات النظام:
- Windows 10 أو أحدث
- 4 جيجا RAM (الحد الأدنى)
- 100 ميجا مساحة فارغة
- لا يحتاج Python مثبت

⚠️ ملاحظات مهمة:
- التشغيل الأول قد يستغرق ثوانٍ إضافية
- لا تحذف الملف أثناء التشغيل
- يمكن نسخ التطبيق إلى أي مكان
- يعمل بدون اتصال بالإنترنت

🛡️ الأمان:
- تشفير كلمات المرور بـ SHA-256
- حماية قواعد البيانات
- لا يحتاج صلاحيات إدارية
- آمن تماماً للاستخدام

📞 الدعم:
- التطبيق يعمل بشكل مستقل
- جميع الوظائف مدمجة
- لا يحتاج اتصال بالإنترنت
- سهل الاستخدام والتشغيل

🎉 استمتع باستخدام نظام إدارة الطلاب المتطور!

تاريخ الإنشاء: 2025-07-06
المطور: تم تطويره بواسطة الذكاء الاصطناعي
