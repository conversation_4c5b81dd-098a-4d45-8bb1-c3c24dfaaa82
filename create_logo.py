#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء شعار للنظام
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_logo():
    """إنشاء شعار النظام"""
    # إنشاء صورة بخلفية شفافة
    width, height = 200, 200
    img = Image.new('RGBA', (width, height), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # رسم دائرة خلفية
    circle_color = (52, 152, 219, 255)  # أزرق
    draw.ellipse([20, 20, width-20, height-20], fill=circle_color)
    
    # رسم دائرة داخلية
    inner_circle_color = (41, 128, 185, 255)  # أزرق داكن
    draw.ellipse([40, 40, width-40, height-40], fill=inner_circle_color)
    
    # محاولة استخدام خط عربي، وإلا استخدام الخط الافتراضي
    try:
        font_large = ImageFont.truetype("arial.ttf", 60)
        font_small = ImageFont.truetype("arial.ttf", 20)
    except:
        font_large = ImageFont.load_default()
        font_small = ImageFont.load_default()
    
    # رسم رمز التعليم
    graduation_symbol = "🎓"
    # نظراً لصعوبة رسم الإيموجي، سنستخدم نص بديل
    text_color = (255, 255, 255, 255)  # أبيض
    
    # رسم النص الرئيسي
    main_text = "SMS"
    bbox = draw.textbbox((0, 0), main_text, font=font_large)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    x = (width - text_width) // 2
    y = (height - text_height) // 2 - 10
    draw.text((x, y), main_text, fill=text_color, font=font_large)
    
    # رسم النص الفرعي
    sub_text = "Student Management"
    bbox = draw.textbbox((0, 0), sub_text, font=font_small)
    text_width = bbox[2] - bbox[0]
    x = (width - text_width) // 2
    y = y + 50
    draw.text((x, y), sub_text, fill=text_color, font=font_small)
    
    # حفظ الصورة
    img.save('logo.png', 'PNG')
    print("تم إنشاء الشعار بنجاح: logo.png")

if __name__ == "__main__":
    create_logo()
